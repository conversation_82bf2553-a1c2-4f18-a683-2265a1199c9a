/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/09
 * desc   :
 * </pre>
 */


object Libs {

    // classpath plugin
    const val ANDROID_BUILD_GRADLE = "com.android.tools.build:gradle:8.2.2"
    const val KOTLIN_GRADLE_PLUGIN = "org.jetbrains.kotlin:kotlin-gradle-plugin:${Version.KOTLIN}"
    const val NAVIGATION_GRADLE_PLUGIN =
        "androidx.navigation:navigation-safe-args-gradle-plugin:${Version.NAVIGATION}"
    const val META_PLUGIN = "com.meta.build.gradle:meta-plugin:2.4.1025"
    const val META_CODE_SKIP = "com.meta.compat:code-skip:0.0.35"
    const val GOOGLE_SERVICES_PLUGIN = "com.google.gms:google-services:4.3.10"
    const val CRASHLYTICS_GRADLE_PLUGIN = "com.google.firebase:firebase-crashlytics-gradle:2.7.1"
    const val AAB_RES_GUARD = "com.bytedance.android:aabresguard-plugin:0.1.10"
    const val MW_GRADLE_PLUGIN = "com.meta.mw.gradle:mw-plugin:1.0.7"

    // jetpack
    const val ACTIVITY = "androidx.activity:activity-ktx"
    const val APPCOMPAT = "androidx.appcompat:appcompat"
    const val ANNOTATIONS = "androidx.annotation:annotation"
    const val CARD_VIEW = "androidx.cardview:cardview"
    const val COLLECTION = "androidx.collection:collection-ktx"
    const val CONCURRENT = "androidx.concurrent:concurrent-futures-ktx"
    const val CONSTRAINT_LAYOUT = "androidx.constraintlayout:constraintlayout"
    const val COORDINATOR_LAYOUT = "androidx.coordinatorlayout:coordinatorlayout"
    const val CORE_KTX = "androidx.core:core-ktx"
    const val FRAGMENT = "androidx.fragment:fragment-ktx"
    const val LIFECYCLE_RUNTIME = "androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
    const val LIFECYCLE_VIEW_MODEL = "androidx.lifecycle:lifecycle-viewmodel-ktx"
    const val LIFECYCLE_LIVEDATA = "androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"
    const val INTERPOLATOR = "androidx.interpolator:interpolator"
    const val NAVIGATION_FRAGMENT = "androidx.navigation:navigation-fragment-ktx:${Version.NAVIGATION}"
    const val NAVIGATION_UI = "androidx.navigation:navigation-ui-ktx:${Version.NAVIGATION}"
    const val PAGING = "androidx.paging:paging-runtime:3.1.1"
    const val RECYCLERVIEW = "androidx.recyclerview:recyclerview"
    const val RECYCLERVIEW_SELECTION = "androidx.recyclerview:recyclerview-selection"
    const val ROOM = "androidx.room:room-runtime:2.5.2"
    const val ROOM_COMPILER = "androidx.room:room-compiler:2.5.2"
    const val ROOM_KTX = "androidx.room:room-ktx:2.5.2"
    const val ROOM_PAGING = "androidx.room:room-paging:2.5.2"
    const val SAVEDSTATE = "androidx.savedstate:savedstate-ktx"
    const val SWIPEREFRESHLAYOUT = "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
    const val TRANSITION = "androidx.transition:transition"
    const val VIEWPAGER2 = "androidx.viewpager2:viewpager2"
    const val PALETTE = "androidx.palette:palette:1.0.0"

    // material
    const val MATERIAL = "androidx.compose.material:material:1.7.1"

    // coroutines
    const val COROUTINES_CORE = "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0"
    const val COROUTINES_ANDROID = "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0"

    // retrofit
    const val RETROFIT = "com.squareup.retrofit2:retrofit:2.11.0"
    const val RETROFIT_GSON = "com.squareup.retrofit2:converter-gson:2.11.0"

    // okhttp
    const val OKHTTP = "com.squareup.okhttp3:okhttp:4.12.0"
    const val OKHTTP_LOGGING = "com.squareup.okhttp3:logging-interceptor:4.12.0"

    // koin
    const val KOIN_CORE = "io.insert-koin:koin-core:3.2.0"
    const val KOIN_ANDROID = "io.insert-koin:koin-android:3.2.0"

    // glide
    const val GLIDE = "com.github.bumptech.glide:glide:${Version.GLIDE}"
    const val GLIDE_COMPILER = "com.github.bumptech.glide:compiler:${Version.GLIDE}"
    const val GLIDE_INTEGRATION = "com.github.bumptech.glide:okhttp3-integration:${Version.GLIDE}"

    // GlideWebpDecoder
    const val GLIDE_APNG = "com.github.zjupure:webpdecoder:2.6.${Version.GLIDE}"

    // banner
    const val BANNER = "io.github.youth5201314:banner:2.2.2"

    // timber
    const val TIMBER = "com.jakewharton.timber:timber:5.0.1"

    // gson
    const val GSON = "com.google.code.gson:gson:2.10.1"

    // mmkv
    const val MMKV = "com.bin.android:mmkv-static:1.3.2.0"

    // lottie
    const val LOTTIE = "com.airbnb.android:lottie:6.3.0"

    // toast
    const val TOAST = "me.drakeet.support:toastcompat:1.1.0"

    // pandora
    const val PANDORA = "com.bin.android:pandora:4.42.0"
    const val PANDORA_CRONET = "com.meta:pandora-cronet:0.0.7"

    // crash report
    const val BUGLY = "com.tencent.bugly:crashreport:4.1.9"
    // 解决 Sharedpreferences 造成的主线程阻塞问题
    const val SP_WAIT_KILLER = "io.github.knight-zxw:spwaitkiller:0.0.8"

    // leakcanary https://github.com/square/leakcanary
    private const val LEAKCANARY_VERSION = "2.14"
    const val LEAKCANARY = "com.squareup.leakcanary:leakcanary-android:${LEAKCANARY_VERSION}"

    // exoplayer
    private const val EXOPLAYER_VERSION = "2.19.1"
    const val EXOPLAYER_CORE = "com.google.android.exoplayer:exoplayer-core:${EXOPLAYER_VERSION}"
    const val EXOPLAYER_UI = "com.google.android.exoplayer:exoplayer-ui:${EXOPLAYER_VERSION}"
    const val EXOPLAYER_DASH = "com.google.android.exoplayer:exoplayer-dash:${EXOPLAYER_VERSION}"

    // 滚轮选择器
    const val WHEEL_VIEW = "com.cncoderx.wheelview:library:1.2.5"
    const val TIKTOK_SDK = "com.bytedance.ies.ugc.aweme:opensdk-oversea-external:0.2.1.0"

    // firebase
    const val FIREBASE_BOM = "com.google.firebase:firebase-bom:32.7.2"
    const val FIREBASE_ANALYTICS = "com.google.firebase:firebase-analytics-ktx"
    const val FIREBASE_CONFIG = "com.google.firebase:firebase-config-ktx"
    const val FIREBASE_FIRESTORE = "com.google.firebase:firebase-firestore-ktx"
    const val FIREBASE_FUNCTIONS = "com.google.firebase:firebase-functions-ktx"
    const val FIREBASE_MESSAGING = "com.google.firebase:firebase-messaging"
    const val FIREBASE_MESSAGING_KTX = "com.google.firebase:firebase-messaging-ktx"
    const val FIREBASE_AUTH = "com.google.firebase:firebase-auth-ktx"
    const val FIREBASE_DYNAMIC_LINK = "com.google.firebase:firebase-dynamic-links-ktx"
    const val FIREBASE_CRASHLYTICS = "com.google.firebase:firebase-crashlytics-ktx"
    const val FIREBASE_CRASHLYTICS_NDK = "com.google.firebase:firebase-crashlytics-ndk"

    // facebook
    const val FACEBOOK_SDK = "com.facebook.android:facebook-android-sdk:15.1.0"

    const val GOOGLE_LOGIN_SDK = "com.google.android.gms:play-services-auth:20.3.0"

    // applovin
    const val APPLOVIN = "com.applovin:applovin-sdk:12.0.0"
    const val APPLOVIN_MEDIATION_ADMOB = "com.applovin.mediation:google-adapter:22.6.0.0"
    const val APPLOVIN_MEDIATION_AD_MANAGER = "com.applovin.mediation:google-ad-manager-adapter:22.6.0.0"
    const val APPLOVIN_MEDIATION_META = "com.applovin.mediation:facebook-adapter:6.16.0.2"


    // MGS
    const val BIZ_MGS = "com.bin.android:biz-mgs:1.4.0"
    const val BIZ_FRIEND = "com.bin.android:biz-friend:0.7.3"
    const val BIZ_IM = "com.bin.android:biz-im:0.17.0"
    const val BIZ_UGC = "com.bin.android:biz-ugc:0.38.3-yc-5557410947-1"

    const val COIL = "io.coil-kt:coil:2.2.2"
    const val COIL_GIF = "io.coil-kt:coil-gif:2.2.2"

    //H5 地址配置
    const val BIZ_CONFIGURABLE_H5 = "com.bin.android:biz-configurable-h5:0.2.0"

    const val META_CLOUD = "com.bin.android:lib-metacloud:1.13.0"

    // audio
    const val LIB_AGORA = "com.bin.android:lib-audio-agora:1.0.1"
    const val LIB_AUDIO_CORE = "com.bin.android:lib-audio-core:1.0.1"

    //aichat
    const val LIB_AI_CHAT = "com.bin.android:lib-meta-ai:0.2.0"

    // 阿里云OSS
    const val LIB_ALIYUN_OSS = "com.aliyun.dpa:oss-android-sdk:2.9.5"

    // 支付宝
    const val LIB_ALIPAY = "com.alipay.sdk:alipaysdk-android:15.8.14"

    // 分享
    const val LIB_META_SHARE = "com.bin.android:lib-share:0.5.1"

    // walle打包 渠道包工具
    const val LIB_WALLE = "com.meituan.android.walle:library:1.1.7"
    // 好像和walle配套的？是我们自己封装的一个，暂不清楚作用，先放在这里
    const val LIB_CHANNEL_EXT = "com.bin.android:lib-channel-ext:0.1.1"

    // 抖音sdk https://open.douyin.com/platform/resource/docs/develop/guide/douyin-sdk/android/
    const val LIB_BYTEDANCE_OPENSDK_CHINA = "com.bytedance.ies.ugc.aweme:opensdk-china-external:*******"

    // 抖音sdk https://open.douyin.com/platform/resource/docs/develop/guide/douyin-sdk/android/
    const val LIB_BYTEDANCE_OPENSDK_COMMON = "com.bytedance.ies.ugc.aweme:opensdk-common:*******"
    // Applog 上报组件
    const val LIB_BYTEDANCE_APP_LOG = "com.bytedance.applog:RangersAppLog-Lite-cn:6.16.9"
    // 商业化转化组件
    const val LIB_BYTEDANCE_ADS = "com.bytedance.ads:AppConvert:1.3.1.9"

    const val LIB_TENCENT = "com.bin.android:lib-metacloud-tencent:1.13.0"
    const val CPBUS = "com.bin.android:cpbus:0.1.4"
    const val EVENT_BUS = "org.greenrobot:eventbus:3.3.1"


    const val PLAY_INSTALL_REFERRER = "com.android.installreferrer:installreferrer:2.2"


    const val JUNIT = "junit:junit:4.12"
    const val JUNIT_EXT = "androidx.test.ext:junit"
    const val ESPRESSO_CORE = "androidx.test.espresso:espresso-core"

    // const val PICTURE_SELECTOR = "io.github.lucksiege:pictureselector:v3.11.2"
    const val PICTURE_SELECTOR = "com.bin.android:lib-picture-selector:3.11.2"
    const val PICTURE_COMPRESS = "io.github.lucksiege:compress:v3.11.2"
    const val PICTURE_CLIP = "io.github.lucksiege:ucrop:v3.11.2"
    const val CAMERA_PREVIEW = "androidx.camera:camera-view:1.0.0-alpha24"
    const val CAMERA_LIFECYCLE = "androidx.camera:camera-lifecycle:1.0.0"
    const val CAMERA2 = "androidx.camera:camera-camera2:1.0.0"

    //MW
    const val MW_HOTFIX = "com.bin.android:mw-hotfix:0.0.9"
    const val MW_BIZ = "com.bin.android:biz-mw:0.1.2"
    const val MW_OODLE = "com.bin.android:lib-oodle:0.0.11"
    const val MW_OODLE_EMPTY = "com.bin.android:lib-oodle-empty:0.0.3"
    const val MW_CORE_LIB = "com.bin.android:mw-core-lib:2025_08_21.25-08-21.10-16.0_0_0"
    const val MW_WECHAT_AUTH_LIB = "com.bin.android:lib-auth-wechat:0.1.0"

    //IPC
    const val IPC = "com.bin.android:lib-ipc:0.1.0"

    const val BASE_RECYCLER_ADAPTER = "com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.0"
    const val SHADOW_LAYOUT = "com.github.lihangleo2:ShadowLayout:3.3.2"
    const val INDICATOR = "com.github.zhpanvip:viewpagerindicator:1.2.1"

    //google play
    const val GOOGLE_PLAY = "com.android.billingclient:billing:8.0.0"
    const val ZOOM_LAYOUT = "com.otaliastudios:zoomlayout:1.9.0"

    const val QCLOUD_COS = "com.qcloud.cos:cos-android-nobeacon:5.9.6"
    const val HOLLOW_TEXT_VIEW = "com.github.FlyJingFish:HollowTextView:1.0.5"

    const val ZXING_CORE = "com.google.zxing:core:3.3.3"
    const val LIFECYCLE_PROCESS = "androidx.lifecycle:lifecycle-process:2.5.1"
    const val META_LINTX = "com.bin.android:lintx:0.1.0"

    private const val epoxyVersion = "5.1.3"
    const val epoxy = "com.airbnb.android:epoxy:$epoxyVersion"
    const val epoxyProcessor = "com.airbnb.android:epoxy-processor:$epoxyVersion"

    // airbnb mavericks
    private const val mavericksVersion = "3.0.5"
    const val mavericks = "com.airbnb.android:mavericks:$mavericksVersion"
    const val mavericksMocking = "com.airbnb.android:mavericks-mocking:$mavericksVersion"
    const val mavericksNavigation = "com.airbnb.android:mavericks-navigation:$mavericksVersion"
    const val mavericksLauncher = "com.airbnb.android:mavericks-launcher:$mavericksVersion"

    const val marqueeView = "com.gongwen:marqueelibrary:1.1.3"


    const val FLEXBOX = "com.google.android.flexbox:flexbox:3.0.0"
    const val uploadCore = "com.bin.android:lib-upload-core:0.6.0"
    const val uploadQiNiu = "com.bin.android:lib-upload-qiniu:0.5.0"
    const val uploadAliyun = "com.bin.android:lib-upload-aliyun:0.5.1-yc-gp-1"
    const val stethoDebug = "com.bin.android:bin-stetho-debug:1.0.0"
    const val stetho = "com.bin.android:bin-stetho:1.0.0"

    //danmuku
    const val DANMU_BYTEDANCE = "com.github.bytedance:danmaku-render-engine:0.1.0"

    const val update = "com.google.android.play:app-update:2.1.0"
    const val update_ktx = "com.google.android.play:app-update-ktx:2.1.0"
    const val libApiResolve = "com.bin.android:lib-api-resolve:0.1.0"


    const val SOLAR_ENGINE_CORE = "com.reyun.solar.engine.oversea:solar-engine-core:1.2.7.9"
    const val SOLAR_ENGINE_PLUGIN_OAID = "com.reyun.solar.engine:se-plugin-oaid:1.2.7.9"
    const val SMART_REFRESH_LAYOUT = "io.github.scwang90:refresh-layout-kernel:2.0.6"

    const val ucCrop = "com.yalantis.io.github.lucksiege:ucrop"

    //download
    const val libDownload = "com.bin.android:lib-hotfix-download:0.3.6"

    // share
    const val TIKTOK_SHARE_CORE = "com.tiktok.open.sdk:tiktok-open-sdk-core:2.3.0"
    const val TIKTOK_SHARE_KIT = "com.tiktok.open.sdk:tiktok-open-sdk-share:2.3.0"
    const val SNAPCHAT_CREATIVE_KIT = "com.snap.creativekit:creativekit:3.0.0"

    const val BIZ_MW_DEVELOP = "com.bin.android:biz-mw-develop-gpark:0.3.2"
    const val BIZ_MW_DEVELOP_PARTY = "com.bin.android:biz-mw-develop-leyuan:0.3.2"
    const val LIB_FONTSCALE = "com.bin.android:fontscale:0.0.5"

    //kwai
    const val KWAI_OPEN_SDK = "com.github.kwaisocial:kwai-opensdk-withauth:3.7.1"

    const val BSPATCH = "com.bin.android:lib-bspatch:0.1.1"
    // oaid
    const val LIB_OAID = "com.bin.android:lib-oaid:0.2.1"
    // 中文转拼音
    const val LIB_PINYIN = "com.github.promeg:tinypinyin:2.0.3"
    // 显示大图
    const val LIB_SUB_SAMPLING_SCALE_IMAGE_VIEW = "com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0"

    // 应用内评分
    const val LIB_IN_APP_REVIEW = "com.google.android.play:review:2.0.2"
    const val LIB_IN_APP_REVIEW_KTX = "com.google.android.play:review-ktx:2.0.2"

    const val LIB_WEBCORE = "com.bin.android:lib-webcore:0.2.7"
    const val LIB_WEBASSETS = "com.bin.android:web-assets:0.2.3"
    const val LIB_WEBBABEL = "com.bin.android:babel:0.2.1"
    const val LIB_LIB_FILE = "com.bin.android:lib-file:0.2.1"

    // 图片查看
    const val LIB_OPEN_IMAGE = "io.github.flyjingfish:openimage-glide:2.4.7"

//    // 透明视频
//    const val LIB_ALPHA_PLAYER = "com.github.bytedance:AlphaPlayer:1.0.6"
}
