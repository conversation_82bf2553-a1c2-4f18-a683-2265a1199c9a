import com.android.build.api.dsl.LibraryProductFlavor
import java.util.Date
import java.util.Locale
import java.util.Properties

plugins {
    id("com.android.library")
    kotlin("android")
    id("com.google.devtools.ksp")
    id("kotlin-parcelize")
    id("androidx.navigation.safeargs.kotlin")
//    id("mw.engine.android")   // 这个插件主要是为了生成几个字段在BuildConfig，目前已经在项目内部处理了，就不去插件里面修改了，有点不可控
    id("multilingual.android.fetch")
    id("multilingual.android.support")
    id("multilingual.android.dump")
}

val dimensionProduct = "product"
val dimensionChannel = "channel"

val flavorNameParty = "party"
val flavorNameGpark = "gpark"
val flavorNameDouyin = "douyin"
val flavorNameDef = "def"


fun createBuildDate(): Date {
    val jenkinsSdkPath = "/data/software/android"
    val rawSdkDir = System.getProperty("sdk.dir")
    val sdkDir = if (rawSdkDir == null) {
        val properties = Properties()
        properties.load(project.rootProject.file("local.properties").inputStream())
        properties.getProperty("sdk.dir")
    } else {
        rawSdkDir
    }

    if (sdkDir != jenkinsSdkPath && gradle.startParameter.taskNames.find {
            it.lowercase(Locale.getDefault()).contains("debug")
        } != null) {
        println("build debug locally,set build date = 0")
        return Date(0)
    } else {
        return Date()
    }
}

val buildTime = createBuildDate()
fun markBuildTime(date: Date) {
    projectDir.listFiles()?.forEach {
        if (it.isFile && it.name.startsWith("build_time_")) {
            it.delete()
        }
    }
    File(projectDir, "build_time_${date.time}").createNewFile()
}
markBuildTime(buildTime)


fun addFlavorConfig(flavor: LibraryProductFlavor, configFlavor: Flavor) {
    EnvConfigs.getBuildConfigs(configFlavor, buildTime).forEach {
        println("BuildConfig ${it.first} ${it.second} ${it.third}")
        flavor.buildConfigField(it.first, it.second, it.third)
    }
    // 这里对 AndroidManifest.xml 中的占位替换无效, 因为当前是库模块
    // 最后主模块合并AndroidManifest.xml的时候,才能找到占位符,才能替换占位符成功
    // 所以 AndroidManifest.xml 的占位符替换, 要放到主模块中
//    EnvConfigs.getManifests(configFlavor).forEach {
//        flavor.manifestPlaceholders[it.key] = it.value
//    }

    EnvConfigs.getResPlaceholder(configFlavor).forEach {
        flavor.resValue(it.first, it.second, it.third)
    }
}

android {
    compileSdk = Version.COMPILE_SDK
    namespace = "com.socialplay.gpark"
    defaultConfig {
        minSdk = Version.MIN_SDK
        targetSdk = Version.TARGET_SDK

        ndk {
            abiFilters.add("arm64-v8a")
            abiFilters.add("armeabi-v7a")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        viewBinding = true
        buildConfig = true
    }

    val main by sourceSets
    main.run {
        java.srcDirs(
            "src/main/java",
            "src/web/java",
        )
        res.srcDirs(
            "src/main/res",
            "src/web/res",
        )
    }

    flavorDimensions += dimensionProduct
    flavorDimensions += dimensionChannel
    val partyImplementation by configurations.creating
    val partyCompileOnly by configurations.creating
    val gparkImplementation by configurations.creating
    val douyinImplementation by configurations.creating
    val douyinCompileOnly by configurations.creating
    productFlavors {
        create(flavorNameParty) {
            dimension = dimensionProduct
            dependencies {
                // 在Flavor内部引用的包，都使用compileOnly的方式引用，因为避免对方的壳子打包的时候包含进去了。然后再在自己的壳子里面去做implementation引入
                partyCompileOnly(fileTree("dir" to "src/party/libs", "include" to listOf("*.jar", "*.aar")))
                // share 微信/QQ
                partyImplementation(Libs.LIB_META_SHARE)
                // crash report
                partyImplementation(Libs.BUGLY)
                // 解决 Sharedpreferences 造成的主线程阻塞问题
                partyImplementation(Libs.SP_WAIT_KILLER)
                // 支付宝
                partyImplementation(Libs.LIB_ALIPAY)
                // 阿里云OSS
                partyImplementation(Libs.LIB_ALIYUN_OSS)
                // 瓦力 美团打包 渠道包工具
                partyImplementation(Libs.LIB_WALLE)
                // 抖音
                partyImplementation(Libs.LIB_BYTEDANCE_OPENSDK_COMMON)
                partyImplementation(Libs.LIB_BYTEDANCE_OPENSDK_CHINA)
                // 快手
                partyImplementation(Libs.KWAI_OPEN_SDK)
                // 小红书等
                partyImplementation(Libs.uploadAliyun)
                partyImplementation(Libs.BSPATCH)
                partyImplementation(Libs.LIB_CHANNEL_EXT)
                // OAID
                partyImplementation(Libs.LIB_OAID)
                // 拼音
                partyImplementation(Libs.LIB_PINYIN)
                // 中文版版扫描二维码测试页面
                partyImplementation(Libs.BIZ_MW_DEVELOP_PARTY)
            }
            addFlavorConfig(this, Flavor.PARTY)
        }
        create(flavorNameGpark) {
            dimension = dimensionProduct
            addFlavorConfig(this, Flavor.G_PARK)
            dependencies {
                gparkImplementation(Libs.uploadQiNiu)
                // 英文版扫描二维码测试页面
                gparkImplementation(Libs.BIZ_MW_DEVELOP)
            }
        }
        create(flavorNameDouyin) {
            dimension = dimensionChannel
            dependencies {
                // 抖音SDK
                douyinCompileOnly(fileTree("dir" to "src/douyin/libs", "include" to listOf("*.jar", "*.aar")))
                douyinImplementation(Libs.LIB_BYTEDANCE_OPENSDK_COMMON)
                douyinImplementation(Libs.LIB_BYTEDANCE_OPENSDK_CHINA)
                douyinImplementation(Libs.LIB_BYTEDANCE_APP_LOG)
                douyinImplementation(Libs.LIB_BYTEDANCE_ADS)
            }
        }
        create(flavorNameDef) {
            dimension = dimensionChannel
        }
    }
//    buildTypes {
//        debug {
//
//        }
//        release {
//
//        }
//    }
}

androidComponents {
    beforeVariants { variant ->
        println("变体 ${variant.buildType} ${variant.productFlavors}")
        if (variant.productFlavors.size >= 2 && variant.productFlavors[0].second == "gpark" && variant.productFlavors[1].second == "douyin") {
            // 过滤掉gpark douyin组合，因为douyin是派对特有的flavor
            variant.enable = false
        }
    }
    onVariants { variant ->
        println("变体2-3 ${variant.name} ${variant.buildType}  ${variant.flavorName} ${variant.productFlavors}")
    }

}

// 禁用 test任务，test任务中有很多配置不对,会导致lint失败
tasks.configureEach {
    if (name.contains("Test")) {
        enabled = false
    }
}

dependencies {
    // libs
//    compileOnly(fileTree(mapOf("dir" to "../libs3rdSdk", "include" to listOf("*.jar", "*.aar"))))
    implementation(fileTree("dir" to "src/main/libs", "include" to listOf("*.jar")))
    implementation(project(":libs3rdSdk"))
    implementation(project(":libs3rdSdkDanmaku"))
    // jetpack
    implementation(Libs.ACTIVITY)
    implementation(Libs.APPCOMPAT)
    implementation(Libs.ANNOTATIONS)
    implementation(Libs.CONSTRAINT_LAYOUT)
    implementation(Libs.COORDINATOR_LAYOUT)
    implementation(Libs.CORE_KTX)
    implementation(Libs.FRAGMENT)
    implementation(Libs.LIFECYCLE_RUNTIME)
    implementation(Libs.LIFECYCLE_VIEW_MODEL)
    implementation(Libs.LIFECYCLE_LIVEDATA)
    implementation(Libs.NAVIGATION_FRAGMENT)
    implementation(Libs.NAVIGATION_UI)
    implementation(Libs.PAGING)
    implementation(Libs.RECYCLERVIEW)
    implementation(Libs.ROOM)
    implementation(Libs.ROOM_KTX)
    implementation(Libs.ROOM_PAGING)
    ksp(Libs.ROOM_COMPILER)
    implementation(Libs.SAVEDSTATE)
    implementation(Libs.SWIPEREFRESHLAYOUT)
    implementation(Libs.TRANSITION)
    implementation(Libs.VIEWPAGER2)
    implementation(Libs.PALETTE)

    // material
    implementation(Libs.MATERIAL)

    // coroutines
    implementation(Libs.COROUTINES_CORE)
    implementation(Libs.COROUTINES_ANDROID)

    // retrofit
    implementation(Libs.RETROFIT)
    implementation(Libs.RETROFIT_GSON)

    // okhttp
    implementation(Libs.OKHTTP)
    implementation(Libs.OKHTTP_LOGGING)

    // koin
    implementation(Libs.KOIN_CORE)
    implementation(Libs.KOIN_ANDROID)

    // glide
    implementation(Libs.GLIDE)
    implementation(Libs.GLIDE_INTEGRATION)
    ksp(Libs.GLIDE_COMPILER)
    implementation(Libs.GLIDE_APNG)

    // banner
    implementation(Libs.BANNER)

    // timber
    implementation(Libs.TIMBER)

    // gson
    implementation(Libs.GSON)

    // mmkv
    implementation(Libs.MMKV)

    // lottie
    implementation(Libs.LOTTIE)

    // toast
    implementation(Libs.TOAST)

    // pandora
    implementation(Libs.PANDORA)
    implementation(Libs.PANDORA_CRONET)

    // leakcanary
//    debugImplementation(Libs.LEAKCANARY)

    // test
//    testImplementation(Libs.JUNIT)
//    androidTestImplementation(Libs.JUNIT_EXT)
//    androidTestImplementation(Libs.ESPRESSO_CORE)

    implementation(Libs.WHEEL_VIEW)

    implementation(Libs.META_CLOUD)
    implementation(Libs.BIZ_IM)
    implementation(Libs.BIZ_UGC)
    implementation(Libs.BIZ_FRIEND)
    implementation(Libs.BIZ_MGS)
    implementation(Libs.LIB_TENCENT)
    implementation(Libs.CPBUS)
    implementation(Libs.EVENT_BUS)

    implementation(Libs.CAMERA_PREVIEW)
    implementation(Libs.CAMERA_LIFECYCLE)
    implementation(Libs.PICTURE_SELECTOR)
    implementation(Libs.PICTURE_COMPRESS)
    implementation(Libs.CAMERA2)
    implementation(Libs.PICTURE_CLIP)

    // MW
    implementation(Libs.MW_HOTFIX)
    implementation(Libs.MW_BIZ)
    implementation(Libs.MW_OODLE)
    implementation(Libs.MW_CORE_LIB)

//    implementation(Libs.MW_OODLE)
    implementation(Libs.ZOOM_LAYOUT)

    implementation(Libs.IPC)

    implementation(Libs.BASE_RECYCLER_ADAPTER)
    implementation(Libs.SHADOW_LAYOUT)
    implementation(Libs.INDICATOR)

    implementation(Libs.EXOPLAYER_CORE)
    implementation(Libs.EXOPLAYER_UI)
    implementation(Libs.EXOPLAYER_DASH)

    implementation(Libs.BIZ_CONFIGURABLE_H5)
    implementation(Libs.QCLOUD_COS)

    implementation(Libs.LIB_AUDIO_CORE)
    implementation(Libs.LIB_AGORA)

    implementation(Libs.HOLLOW_TEXT_VIEW)

    implementation(Libs.ZXING_CORE)
    implementation(Libs.PLAY_INSTALL_REFERRER)
    implementation(Libs.LIFECYCLE_PROCESS)
    lintChecks(Libs.META_LINTX)

    implementation(Libs.epoxy) { exclude(group = "com.android.support") }
    implementation(Libs.mavericks)
    implementation(Libs.mavericksNavigation)

    implementation(Libs.libApiResolve)
    implementation(Libs.uploadCore)
    implementation(Libs.marqueeView)

    implementation(Libs.FLEXBOX)
    debugImplementation(Libs.stethoDebug)
    releaseImplementation(Libs.stetho)
    implementation(Libs.LIB_AI_CHAT)
    implementation(Libs.libDownload)
    val leakcanaryopen: String by rootProject
    if (leakcanaryopen == "true") {
        debugImplementation(Libs.LEAKCANARY)
    }

    implementation(Libs.LIB_FONTSCALE)
    implementation(Libs.COIL)
    implementation(Libs.COIL_GIF)
    implementation(Libs.LIB_SUB_SAMPLING_SCALE_IMAGE_VIEW)
    api(Libs.LIB_WEBCORE) { exclude(group = "com.bin.android", module = "babel") }
    api(Libs.LIB_WEBASSETS)
    implementation(Libs.LIB_WEBBABEL)
    implementation(Libs.LIB_LIB_FILE)
    implementation(Libs.LIB_OPEN_IMAGE)
//    implementation(Libs.LIB_ALPHA_PLAYER)
}