//
//import android.app.Activity
//import android.content.Context
//import androidx.fragment.app.Fragment
//import androidx.lifecycle.ViewModel
//import androidx.lifecycle.viewModelScope
//import com.airbnb.mvrx.MavericksState
//import com.socialplay.gpark.EnvConfig
//import com.socialplay.gpark.R
//import com.socialplay.gpark.data.IMetaRepository
//import com.socialplay.gpark.data.base.LoginState
//import com.socialplay.gpark.data.base.data
//import com.socialplay.gpark.data.base.succeeded
//import com.socialplay.gpark.data.interactor.AccountInteractor
//import com.socialplay.gpark.data.kv.MetaKV
//import com.socialplay.gpark.data.model.LOGIN_TYPE_GPARK_ID
//import com.socialplay.gpark.data.model.LoginSource
//import com.socialplay.gpark.data.model.LoginType
//import com.socialplay.gpark.data.model.LoginWay
//import com.socialplay.gpark.data.model.user.ContinueAccountInfo
//import com.socialplay.gpark.data.model.user.OAuthResponse
//import com.socialplay.gpark.function.auth.oauth.IOAuthCallback
//import com.socialplay.gpark.function.auth.oauth.OAuthManager
//import com.socialplay.gpark.function.router.MetaRouter
//import kotlinx.coroutines.flow.Flow
//import kotlinx.coroutines.flow.MutableSharedFlow
//import kotlinx.coroutines.flow.MutableStateFlow
//import kotlinx.coroutines.flow.SharedFlow
//import kotlinx.coroutines.flow.combine
//import kotlinx.coroutines.launch
//import timber.log.Timber
//
///**
// * @author: ning.wang
// * @date: 2021-09-24 8:44 下午
// * @desc:
// */
//
//class LoginViewModel(
//    private val repository: IMetaRepository,
//    private val accountInteractor: AccountInteractor,
//    private val metaKV: MetaKV
//) : ViewModel(), IOAuthCallback {
//
//    companion object {
//        const val LOCATION_GUIDE_LOGIN = "1"
//        const val LOCATION_SIGN_UP = "2"
//        const val LOCATION_LOGIN = "3"
//
//        const val STATUS_SUCCESS = "1"
//        const val STATUS_FAIL = "2"
//        const val STATUS_CANCEL = "3"
//
//        const val MODE_ACCOUNT_EMAIL = 0
//        const val MODE_GPARK_ID = 1
//    }
//
//    private val _modeFlow = MutableStateFlow<Int>(MODE_GPARK_ID)
//    val modeFlow: Flow<Int> = _modeFlow
//
//    private val _accountFlow = MutableStateFlow<String?>(null)
//    val accountFlow: Flow<String?> = _accountFlow
//
//    private val _gparkIdFlow = MutableStateFlow<String?>(null)
//    val gparkIdFlow: Flow<String?> = _gparkIdFlow
//
//    private val _continueAccountInfo = MutableStateFlow<ContinueAccountInfo?>(null)
//    val continueAccountInfo: Flow<ContinueAccountInfo?> get() = _continueAccountInfo
//
//    private val _passwordFlow = MutableStateFlow<String?>(null)
//    val passwordFlow: Flow<String?> = _passwordFlow
//
//    private val _passwordVisibilityFlow = MutableStateFlow(false)
//    val passwordVisibilityFlow: Flow<Boolean> = _passwordVisibilityFlow
//
//    val accountAndPasswordValidFlow: Flow<Boolean> = combine(
//        _modeFlow, _accountFlow, _gparkIdFlow, _passwordFlow
//    ) { mode, account, gparkId, password ->
//        ((mode == MODE_ACCOUNT_EMAIL && isAccountValid(account)) || (mode == MODE_GPARK_ID && isGparkIdValid(
//            gparkId
//        ))) && isPasswordValid(password)
//    }
//
//    val currentAccountFlow: Flow<String?> = combine(
//        _modeFlow, _accountFlow, _gparkIdFlow
//    ) { mode, account, gparkId ->
//        when (mode) {
//            MODE_ACCOUNT_EMAIL -> account
//            MODE_GPARK_ID -> gparkId
//            else -> null
//        }
//    }
//
//    private val _signInStatusFlow = MutableSharedFlow<LoginState<*>>()
//    val signInStatusFlow: SharedFlow<LoginState<*>> = _signInStatusFlow
//
//    /**
//     * [com.socialplay.gpark.data.model.LoginSource]
//     */
//    private var loginSource: String? = null
//
//    /**
//     * [com.socialplay.gpark.data.model.LoginType]
//     */
//    private var loginType: String? = null
//    private var onlyLogin: Boolean = false
//    private var location: String = ""
//
//    val currentAccount: String?
//        get() = when (_modeFlow.value) {
//            MODE_ACCOUNT_EMAIL -> {
//                _accountFlow.value
//            }
//
//            MODE_GPARK_ID -> {
//                _gparkIdFlow.value
//            }
//
//            else -> {
//                null
//            }
//        }
//
//    val mode get() = _modeFlow.value
//
//    init {
//    }
//
//    fun init(loginSource: String, onlyLogin: Boolean, location: String) {
//        this.loginSource = loginSource
//        this.onlyLogin = onlyLogin
//        this.location = location
//    }
//
//    private fun isPasswordValid(password: String?): Boolean {
//        return !password.isNullOrBlank() && password.length >= 6 && password.length <= 16
//    }
//
//    private fun isAccountValid(account: String?): Boolean {
//        return !account.isNullOrBlank() && account.length >= 3
//    }
//
//    private fun isGparkIdValid(gparkId: String?): Boolean {
//        return !gparkId.isNullOrBlank()
//    }
//
//    fun auth(authWay: LoginWay, activity: Activity, source: String, loginType: LoginType) =
//        viewModelScope.launch {
//            dispatchCallbackStatus(LoginState.Loading)
//            OAuthManager.oauth(authWay, activity, source, loginType, oAuthCallback = this@LoginViewModel)
//        }
//
//    fun authAccount(context: Context, loginType: LoginType) {
//        when (_modeFlow.value) {
//            MODE_ACCOUNT_EMAIL -> {
//                loginByAccount(context, loginType)
//            }
//
//            MODE_GPARK_ID -> {
//                loginByGparkId(context, loginType)
//            }
//        }
//    }
//
//    private fun loginByAccount(context: Context, loginType: LoginType) = viewModelScope.launch {
//        dispatchCallbackStatus(LoginState.Loading)
//        val account = _accountFlow.value
//        val password = _passwordFlow.value
//        if (account.isNullOrBlank()) {
//            dispatchCallbackStatus(
//                LoginState.Failed(
//                    context.getString(R.string.cannot_empty)
//                        .format(context.getString(R.string.text_account)), 0, null
//                )
//            )
//            return@launch
//        }
//        if (password.isNullOrBlank()) {
//            dispatchCallbackStatus(
//                LoginState.Failed(
//                    context.getString(R.string.cannot_empty)
//                        .format(context.getString(R.string.text_password)), 0, null
//                )
//            )
//            return@launch
//        }
//
//        accountInteractor.accountLogin(account, password, loginType).collect {
//            dispatchCallbackStatus(it)
//            if (it is LoginState.Succeeded) {
//                metaKV.account.lastLoginType = LoginWay.Account.way
//            }
//        }
//    }
//
//    private fun loginByGparkId(context: Context, loginType: LoginType) = viewModelScope.launch {
//        dispatchCallbackStatus(LoginState.Loading)
//        val gparkId = _gparkIdFlow.value
//        val password = _passwordFlow.value
//        if (gparkId.isNullOrBlank()) {
//            dispatchCallbackStatus(
//                LoginState.Failed(
//                    context.getString(R.string.cannot_empty)
//                        .format(context.getString(R.string.gpark_id)), 0, null
//                )
//            )
//            return@launch
//        }
//        if (password.isNullOrBlank()) {
//            dispatchCallbackStatus(
//                LoginState.Failed(
//                    context.getString(R.string.cannot_empty)
//                        .format(context.getString(R.string.text_password)), 0, null
//                )
//            )
//            return@launch
//        }
//
//        accountInteractor.gparkIdLogin(gparkId, password, loginType).collect {
//            dispatchCallbackStatus(it)
//            if (it is LoginState.Succeeded) {
//                metaKV.account.lastLoginType = LOGIN_TYPE_GPARK_ID
//            }
//        }
//    }
//
//    private suspend fun dispatchCallbackStatus(state: LoginState<*>) {
//        _signInStatusFlow.emit(state)
//    }
//
//    override fun onSuccess(response: OAuthResponse) {
//        if (loginSource.toString() != response.source) {
//            //避免app停留在登录界面，P12授权拉起第三方Google,facebook登录导致出发两次账号绑定
//            return
//        }
//        Timber.d("AuthSuccess $response")
//        viewModelScope.launch {
//            accountInteractor.loginByAuthResponse(response, onlyLogin, location).collect {
////                if (it.succeeded) {
////                    Analytics.track(EventConstants.EVENT_LOGIN_SUCCEED) {
////                        put(EventConstants.KEY_LOGIN_WAY, response.oauthThirdWay.way)
////                        put(EventConstants.KEY_LOGIN_TYPE, response.loginType ?: "")
////                    }
////                }
//                dispatchCallbackStatus(it)
//            }
//        }
//    }
//
//    override fun onFailed(oauthThirdWay: LoginWay, msg: String?, code: Int, reason: String?) {
//        accountInteractor.loginAuthFailed(oauthThirdWay, msg, code, reason, location)
//        viewModelScope.launch {
//            dispatchCallbackStatus(LoginState.Failed(msg ?: "", code, null))
//        }
//    }
//
//    override fun onCancel(oauthThirdWay: LoginWay) {
//        accountInteractor.loginAuthCancel(oauthThirdWay, location)
//        viewModelScope.launch {
//            dispatchCallbackStatus(LoginState.UserCanceled)
//        }
//    }
//
//    override fun onCleared() {
//        super.onCleared()
//    }
//
//    fun togglePasswordVisibility() {
//        _passwordVisibilityFlow.value = !_passwordVisibilityFlow.value
//    }
//
//    fun postAccountValueChanged(account: String?) {
//        when (_modeFlow.value) {
//            MODE_ACCOUNT_EMAIL -> {
//                _accountFlow.value = account
//            }
//
//            MODE_GPARK_ID -> {
//                _gparkIdFlow.value = account
//            }
//        }
//    }
//
//    fun postPasswordValueChanged(password: String?) {
//        _passwordFlow.value = password
//    }
//
//    fun updateMode(mode: Int) {
//        if (!EnvConfig.isParty()) {
//            _modeFlow.value = mode
//        }
//    }
//
//    fun switchMode() {
//        if (!EnvConfig.isParty()) {
//            when (_modeFlow.value) {
//                MODE_ACCOUNT_EMAIL -> {
//                    _modeFlow.value = MODE_GPARK_ID
//                }
//
//                MODE_GPARK_ID -> {
//                    _modeFlow.value = MODE_ACCOUNT_EMAIL
//                }
//            }
//        }
//    }
//
//    fun fetchLoginOthers(): List<LoginItemData> {
//        return accountInteractor.fetchLoginOthers()
//    }
//
//
//    fun getContinueAccount() = viewModelScope.launch {
//        repository.getContinueAccount().collect {
//            if (it.succeeded) {
//                _continueAccountInfo.emit(it.data)
//            }
//        }
//    }
//
//    fun continueLogin(fragment: Fragment, continueInfo: ContinueAccountInfo): Boolean {
//        val source = loginSource ?: LoginSource.GuideLoginContinue.source
//        val loginWay = LoginWay.getLoginType(continueInfo.loginType) ?: return false
//        if (LoginSpecialWrapper.continueLogin(fragment, loginWay, source, continueInfo)) {
//            // 如果特殊flavor处理了，就不用往下走了
//            return true
//        }
//        if (loginWay == LoginWay.Account) {
//            // 账号登录
//            MetaRouter.Login.login(
//                fragment,
//                loginSource = source,
//                onlyLogin = true,
//                continueAccountInfo = continueInfo,
//                successToMain = true
//            )
//        } else {
//            OAuthManager.oauth(loginWay, fragment.requireActivity(), source, LoginType.LastAccount, oAuthCallback = this)
//        }
//        return true
//    }
//
//}