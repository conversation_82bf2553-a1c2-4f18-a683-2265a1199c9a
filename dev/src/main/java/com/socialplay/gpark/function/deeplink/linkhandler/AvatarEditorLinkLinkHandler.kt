package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

// adb shell am start -a android.intent.action.VIEW -d "gpark://gpark.fun?action=avatarEditor&categoryId=1&data=xxxx"
class AvatarEditorLinkLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink AvatarEditorLinkLinkHandler handle uri:%s", data.uri)

        val categoryId =
            data.uri.getQueryParameter(MetaDeepLink.PARAM_CATEGORY_ID)?.toIntOrNull() ?: CategoryId.SCHEME_DEFAULT

        val opacityData = data.uri.getQueryParameter("data")

        // 验证 categoryId 是否有效，避免传入无效值导致崩溃
        if (categoryId <= 0) {
            Timber.w("Invalid categoryId from deeplink: %d, using default", categoryId)
            MetaRouter.MobileEditor.avatarEditor(data.activity, CategoryId.SCHEME_DEFAULT, opacityData)
        } else {
            MetaRouter.MobileEditor.avatarEditor(data.activity, categoryId, opacityData)
        }

        chain.handle(data)

        return LinkHandleResult.Success
    }
}