package com.socialplay.gpark.function.deeplink.linkhandler

import android.os.Bundle
import com.socialplay.gpark.function.deeplink.BundleConverter
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber


/**
 * 处理最基本的跳转
 * 根据页面的ID名称来跳转,最低级的跳转,大部分情况应该都不会使用这个
 * https://233xyx.com?action=navigate&data=base64EncodedData
 */
class BasicNavigateLinkHandler : LinkHandler {

    companion object {

        fun encodeBundle(data: Bundle): String {
            return BundleConverter.encode(data)
        }

        fun decodeBundle(data: String): Bundle {
            return BundleConverter.decode(data)
        }
    }

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val destPageName = data.uri.getQueryParameter("dest") ?: return LinkHandleResult.Failed("dest is null")

        val destId = data.activity.resources.getIdentifier(destPageName, "id", data.activity.packageName)
        Timber.i("BasicNavigateLinkHandler:dest=%s", destId)
        if (destId <= 0) {
            return LinkHandleResult.Failed("destId is invalid")
        }

        var arguments = Bundle()
        data.uri.getQueryParameter("data")?.let { arguments = decodeBundle(it) }

        Timber.i("BasicNavigateLinkHandler:data=%s", arguments)

        MetaRouter.Control.navigate(data.navHost, destId, arguments)

        chain.handle(data)
        return LinkHandleResult.Success
    }
}