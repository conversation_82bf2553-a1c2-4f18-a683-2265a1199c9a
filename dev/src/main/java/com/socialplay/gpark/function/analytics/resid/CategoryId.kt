package com.socialplay.gpark.function.analytics.resid

import com.socialplay.gpark.function.pandora.PandoraToggle

/**
 * create by: bin on 2020-02-26
 */
object CategoryId {
    /**
     * scheme跳转兜底
     */
    const val SCHEME_DEFAULT = 101
    /**
     * 审核游戏
     */
    const val GAME_REVIEW = 2001

    /**
     * 输入gameId测试游戏
     */
    const val MW_TEST_GAME = 2002

    /**
     * 超级推荐位
     */
    const val SUGGEST_GAME = 2003

    /**
     * web：暂时不会用到
     */
    const val WEB_GAME = 2004

    /**
     * 新手引导遮罩：暂时不会用到
     */
    const val GUIDE_GAME = 2005

    /**
     * 首页跟房进入游戏
     */
    const val HOME_FLOW_ROOM = 2006

    /**
     * 加入语音房的资源位id
     */
    const val VOICE_ROOM = 2007

    /**
     * 消息页-PGC分享卡片
     */
    const val IM_PGC_CARD = 2018

    /**
     * 消息页-UGC分享卡片
     */
    const val IM_UGC_CARD = 2019

    /**
     * 消息页-视频流分享卡片
     */
    const val IM_VIDEO_FEED_CARD = 2020

    /**
     * 社区视频流
     */
    const val COMMUNITY_VIDEO_FEED = 3110

    /**
     * 推荐视频列表-二级页
     */
    const val RECOMMEND_VIDEO_LIST = 3100

    const val MAIN = 3000

    /**
     * 游戏库页：游戏feed
     */
    const val HOME_RECOMMEND_NEWEST_GAME = 3001

    /**
     * 游戏库页：分类模式
     */
    val HOME_RECOMMEND_CATEGORY = if(PandoraToggle.isHomeRecommend) 3067 else 3002

    /**
     * 游戏库页：分类模式-上方banner
     */
    const val HOME_RECOMMEND_CATEGORY_BANNER = 3003

    /**
     * 全部房间列表
     */
    const val HOME_ROOM_LIST = 3004

    /**
     * 手动创建房间
     */
    const val HOME_ROOM_CREATE = 3005

    /**
     * 全部ugc卡片列表页
     */
    const val UGC_ALL = 3006

    /**
     * 首页插卡-二级模板详情页
     */
    const val TEMPLATE_DETAIL = 3007

    /**
     * 首页插卡-二级模组/服装资源详情页
     */
    const val MODULE_DETAIL = 3008

    /**
     * 游戏库页：我玩过的
     */
    const val RECOMMEND_MY_GAMES = 3602

    /**
     * 首页-新游TAB
     */
    const val HOME_NEW_GAMES = 3801

    /**
     * 好友邀请
     */
    const val MGS_INVITE = 5101

    /**
     * 我的页面：最近玩过
     */
    const val HOME_PAGE_RECENT_GAME = 5201

    /**
     * 我的页面：评论
     */
    const val HOME_PAGE_REVIEW = 5202

    /**
     * 我的页面：已发布作品
     */
    const val HOME_PAGE_CREATE = 5203

    /**
     * 我的页面：小屋
     */
    const val HOME_PAGE_HOME = 5204

    /**
     * 其他人我的页面：最近玩过
     */
    const val OTHER_HOME_PAGE_RECENT_GAME = 5301

    /**
     * 其他人我的页面：评论
     */
    const val OTHER_HOME_PAGE_REVIEW = 5302

    /**
     * 其他人我的页面：已发布作品
     */
    const val OTHER_HOME_PAGE_CREATE = 5303

    /**
     * 其他人我的页面：小屋
     */
    const val OTHER_HOME_PAGE_HOME = 5304

    // 详情页TS房间
    const val DETAIL_TS_ROOM = 5701

    // 本地工程列表页
    const val MOBILE_EDITOR_LOCAL = 7001

    // 探索tab页
    const val MOBILE_EDITOR_MAIN = 7002

    // 好友小屋：拜访朋友
    const val NEIGHBOR_VISIT_FRIEND = 5104

    // 好友小屋：菜单回家
    const val NEIGHBOR_POPUP_GO_HOME = 5102

    // 好友小屋：回家
    const val NEIGHBOR_BUTTON_GO_HOME = 5103

    // 好友小屋：新建模板
    const val NEIGHBOR_CREATE_HOME = 5105

    // 好友小屋：去广场
    const val NEIGHBOR_PLAZA_ADD_FRIEND = 5106

    // 模板列表页
    const val MOBILE_EDITOR_TEMPLATE = 7003

    // 发布列表页
    const val MOBILE_EDITOR_PUBLISH = 7004

    // 我的喜欢列表页
    const val MOBILE_EDITOR_MY_LIKE = 7005

    // 过渡板tab
    const val MOBILE_EDITOR_MAIN_TRANSITION = 7006

    // 探索页：小屋
    const val EXPLORE_HOME = 7101

    //角色游戏编辑页面
    const val ROLE_GAME_EDIT_START = 7201

    //角色游戏预加载
    const val ROLE_GAME_PRE_LOAD = 7203

    //扫码拉起
    const val QR_CODE_START = 7204

    // 游戏跳游戏：跨进程
    const val JUMP_GAME_DIF_PROCESS = 7731

    //游戏跳游戏：同进程
    const val JUMP_GAME_SAME_PROCESS = 7732

    //客户端创建角色进入角编
    const val MOBILE_CREATE_TO_ROLE = 7733

    //7735：从im跳角色
    const val JUMP_ROLE_GAME_FROM_IM = 7735

    //7736：从我的profile跳角色
    const val JUMP_ROLE_GAME_FROM_ME_PROFILE = 7736

    //7737：从其他人profile跳角色
    const val JUMP_ROLE_GAME_FROM_OTHER_PROFILE = 7737

    //7738：从头像跳角色
    const val JUMP_ROLE_GAME_FROM_PROFILE_PHOTO = 7738

    //访问相册申请权限资源位
    const val PERMISSION_ALBUM_REQUEST = 7802

    // 派对列表页资源位
    const val PARTY = 8001

    // 新建造页-模板
    const val UGC_BUILD_TEMPLATE = 5001

    // 新建造页-本地
    const val UGC_BUILD_LOCAL = 5002

    // 新建造页-已发布
    const val UGC_BUILD_PUBLISHED = 5003

    // 运营位：建造banner
    const val OPERATION_EDITOR_BANNER = 4752

    // 分享链接进入UGC详情页
    const val UGC_DETAIL_SHARE = 7901

    // UGC详情页做同款
    const val UGC_DETAIL_CRAFT_SAME = 7902

    // ugc造物岛首页
    const val UGC_CRAFT_LAND_HOME = 7903
    //mgs卡片 todo 需要定义当前id
    const val MGS_UGC_CARD = 1212212
    // 系统消息页categoryid
    const val SYS_MESSAGE = 8005

    /**
     * 社区feed跟房
     */
    const val COMMUNITY_JOIN_ROOM = 2010

    /**
     * 社区feed游戏卡片
     */
    const val COMMUNITY_GAME_CARD = 2008

    /**
     * 全部房间列表
     */
    const val COTTAGE_ROOM_LIST = 2009

    /**
     * 剧情模版页
     */
    const val PLOT_PAGE = 7003

    /**
     * 剧情模版游戏内
     */
    const val PLOT_IN_GAME = 7740

    /**
     * 穿搭分享
     * 2011 发帖 | 2012 帖子列表 | 2013 帖子详情
     */
    const val OUTFIT_SHARE_POST_EDIT = 2011
    const val OUTFIT_SHARE_POST_FEED = 2012
    const val OUTFIT_SHARE_POST_DETAIL = 2013

    /**
     * 角色Tab Popular和 Moments UGC Party启动游戏用的资源位
     */
    const val AVATAR_TAB_LAUNCH_GAME = 2014

    /**
     * 选择模式-世界地图
     */
    const val SELECT_MODE_WORLD_MAP = 1001
    /**
     * 话题广场
     */
    const val TOPIC_SQUARE = 2015

    /**
     * 话题详情页
     */
    const val TOPIC_DETAIL = 2016

    /**
     * 个人主页穿搭tab
     */
    const val PROFILE_OUTFIT_TAB = 5205

    /**
     * 运营位：角色编辑器首页banner
     */
    const val OPERATION_POSITION_EDITOR_HOME_BANNER = 2101


    /**
     * 运营位：角色编辑器首页弹窗
     */
    const val OPERATION_EDITOR_HOME_POPUP = 2102

    /**
     * 飞轮位
     */
    const val CATEGORY_ID_ROLE_FLY_WHEEL = 2017

    const val SEARCH_RESULT = 9001

    const val HOME_RECOMMEND = 3066

    const val SEARCH_POST = 4830


    // discover页面ugc飞轮位
    const val CATEGORY_ID_KOL_FLY_WHEEL = 7801

    //discover页面活动位
    const val CATEGORY_ID_KOL_BANNER = 7802

    //关注作者的ugc作品
    const val CATEGORY_ID_KOL_FOLLOWED_UGC = 7803

    //关注作者的ugc作品二级页
    const val CATEGORY_ID_KOL_FOLLOWED_UGC_DETAIL = 7804

    //ugc作品推荐
    const val CATEGORY_ID_KOL_RECOMMEND_UGC = 7805

    //ugc作品推荐二级页
    const val CATEGORY_ID_KOL_RECOMMEND_UGC_DETAIL = 7806

    //ugc推荐feed流
    const val CATEGORY_ID_KOL_LABEL_UGC = 7807

    //个人主页ugc作品主态
    const val CATEGORY_ID_HOME_PAGE_UGC_MINE = 7808

    //个人主页ugc作品客态
    const val CATEGORY_ID_HOME_PAGE_UGC_OTHERS = 7809

    // +号底栏进post
    const val CATEGORY_ID_HOME_ADD_POSTS = 9004

    // +号底栏进moments
    const val CATEGORY_ID_HOME_ADD_MOMENTS = 9005

    // 资源底栏
    const val ASSETS_FEED = 9009

    /**
     * 选择备份进入本地工程
     */
    const val UGC_BACKUP = 12001

    /**
     * ugc服装
     */
    // 首页底栏-素材库tab
    const val UGC_DESIGN_FEED = 9008
    // ugc服装详情页
    const val UGC_DESIGN_DETAIL = 10001
    // 个人主页-素材库tab
    const val UGC_DESIGN_PROFILE_DESIGN_TAB = 10002
    // 个人主页-当前穿搭
    const val UGC_DESIGN_PROFILE_CLOTHES = 10003
    // 帖子详情-服装卡片
    const val UGC_DESIGN_POST_DETAIL = 10004
    // 私聊-服装卡片
    const val UGC_DESIGN_IM_CARD = 10005
    // 帖子列表-服装卡片
    const val UGC_DESIGN_POST_FEED = 10007
    // 资源二级列表
    const val UGC_ASSET_LIST = 320
    // ugc模板-新建工程
    const val UGC_MODULE_TEMPLATE = 321
    const val UGC_MODULE_TEMPLATE_CONTINUE = 322
    // 模组banner
    const val UGC_MODULE_BANNER = 401
    const val MAIN_ADD_DIALOG = 7006
    // ugc模组引导
    const val UGC_MODULE_GUIDE = 324
    const val GAME_SHARE_CARD_PGC = 11001
    const val GAME_SHARE_CARD_UGC = 11002

    const val PGC_GAME_DETAIL = 11003
    const val UGC_GAME_DETAIL = 11004

    /**
     * 未发布项目提醒弹框
     */
    const val UNPUBLISHED_PROJECT_REMINDER = 12002

    // 个人主页banner
    const val OPERATION_POSITION_PROFILE_BANNER = 5206

    // ugc模组-我的
    const val UGC_MODULE_MINE = 9002

    // ugc激励弹窗
    const val UGC_COMMERCIAL_PROMOTION_DIALOG = 12003

    /**
     * 游戏详情页模板外放资源位
     */
    const val GAME_DETAIL_TEMPLATE = 3200L

    /**
     * 游戏详情页-资源栏展示资源位
     */
    const val GAME_DETAIL_ASSETS_TOOLKIT = 3201L

    /**
     * 资源工具推荐二级页
     */
    const val ASSETS_TOOLKIT_DETAIL = 3202L

    /**
     * 资源工具推荐二级页-资源栏推荐流
     */
    const val ASSETS_TOOLKIT_DETAIL_RECOMMEND = 3203L

    /**
     * 全局截图跳转发帖页
     */
    const val SCREENSHOT_SHARE_POST = 20001

    /**
     * 社区帖子重发
     */
    const val COMMUNITY_POST_RESEND = 2021
}
